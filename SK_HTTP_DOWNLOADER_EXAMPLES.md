# SK HTTP Downloader 实用示例和测试指南

## 完整使用示例

### 1. 基础下载和播放示例

```c
#include "sk_http_downloader.h"
#include "sk_log.h"

static const char *TAG = "AudioExample";

/**
 * @brief 完整的音频下载播放示例
 */
void example_complete_audio_workflow(void) {
    SkHttpDownloadData downloadData;
    sk_err_t ret;
    
    SK_LOGI(TAG, "=== 开始音频下载播放流程 ===");
    
    // 步骤1: 下载OPUS音频文件
    const char *audio_url = "http://example.com/test_audio.opus";
    ret = SkHttpDownloadFile(audio_url, &downloadData);
    
    if (ret != SK_RET_SUCCESS) {
        SK_LOGE(TAG, "下载失败: %d", ret);
        return;
    }
    
    SK_LOGI(TAG, "✅ 下载成功: %zu bytes", downloadData.totalSize);
    
    // 步骤2: 解析OPUS头部信息
    SkOpusHeader header;
    ret = SkOggParseOpusHeader(downloadData.ringbuf, &header);
    
    if (ret == SK_RET_SUCCESS) {
        SK_LOGI(TAG, "🎵 音频信息:");
        SK_LOGI(TAG, "   - 声道数: %d", header.channels);
        SK_LOGI(TAG, "   - 采样率: %lu Hz", header.sampleRate);
        SK_LOGI(TAG, "   - 预跳过: %d samples", header.preSkip);
        SK_LOGI(TAG, "   - 版本: %d", header.version);
    } else {
        SK_LOGW(TAG, "⚠️ 无法解析音频头部: %d", ret);
    }
    
    // 步骤3: 直接播放音频
    SK_LOGI(TAG, "🔊 开始播放音频...");
    ret = SkOpusQueueStreamPlay(&downloadData);
    
    if (ret == SK_RET_SUCCESS) {
        SK_LOGI(TAG, "✅ 播放完成");
    } else {
        SK_LOGE(TAG, "❌ 播放失败: %d", ret);
    }
    
    // 步骤4: 清理资源
    SkOggResetParser();
    SkHttpDownloadFree(&downloadData);
    
    SK_LOGI(TAG, "=== 音频流程完成 ===");
}
```

### 2. 手动解析和处理示例

```c
/**
 * @brief 手动解析OPUS包的示例
 */
void example_manual_opus_parsing(void) {
    SkHttpDownloadData downloadData;
    sk_err_t ret;
    
    // 下载文件
    ret = SkHttpDownloadFile("http://example.com/music.opus", &downloadData);
    if (ret != SK_RET_SUCCESS) {
        SK_LOGE(TAG, "下载失败");
        return;
    }
    
    // 解析头部
    SkOpusHeader header;
    if (SkOggParseOpusHeader(downloadData.ringbuf, &header) == SK_RET_SUCCESS) {
        SK_LOGI(TAG, "音频格式: %dch, %luHz", header.channels, header.sampleRate);
    }
    
    // 手动提取和处理每个OPUS包
    SkOpusPacket packet;
    int packetCount = 0;
    size_t totalAudioBytes = 0;
    
    while (SkOggGetNextOpusPacket(downloadData.ringbuf, &packet) == SK_RET_SUCCESS) {
        packetCount++;
        totalAudioBytes += packet.size;
        
        SK_LOGI(TAG, "包 #%d: %zu bytes", packetCount, packet.size);
        
        // 这里可以添加自定义处理逻辑
        // 例如：保存到文件、发送到网络、自定义解码等
        
        // 处理完成后释放包
        SkOggFreeOpusPacket(&packet);
        
        // 限制处理包数量（用于测试）
        if (packetCount >= 10) {
            SK_LOGI(TAG, "已处理前10个包，停止解析");
            break;
        }
    }
    
    SK_LOGI(TAG, "解析完成: %d个包, 总计%zu字节音频数据", 
           packetCount, totalAudioBytes);
    
    // 清理资源
    SkOggResetParser();
    SkHttpDownloadFree(&downloadData);
}
```

### 3. 错误处理和重试示例

```c
/**
 * @brief 带重试机制的下载示例
 */
sk_err_t example_download_with_retry(const char *url, int max_retries) {
    SkHttpDownloadData downloadData;
    sk_err_t ret;
    
    for (int attempt = 1; attempt <= max_retries; attempt++) {
        SK_LOGI(TAG, "下载尝试 %d/%d: %s", attempt, max_retries, url);
        
        ret = SkHttpDownloadFile(url, &downloadData);
        
        if (ret == SK_RET_SUCCESS) {
            SK_LOGI(TAG, "✅ 下载成功 (尝试 %d)", attempt);
            
            // 验证下载的数据
            if (downloadData.totalSize > 0) {
                SK_LOGI(TAG, "文件大小: %zu bytes", downloadData.totalSize);
                
                // 可以添加更多验证逻辑
                // 例如：检查文件格式、计算校验和等
                
                SkHttpDownloadFree(&downloadData);
                return SK_RET_SUCCESS;
            } else {
                SK_LOGW(TAG, "下载的文件为空");
                SkHttpDownloadFree(&downloadData);
                ret = SK_RET_FAIL;
            }
        }
        
        // 记录失败原因
        switch (ret) {
            case SK_RET_NO_MEMORY:
                SK_LOGE(TAG, "❌ 内存不足");
                break;
            case SK_RET_FAIL:
                SK_LOGE(TAG, "❌ 网络或服务器错误");
                break;
            case SK_RET_INVALID_PARAM:
                SK_LOGE(TAG, "❌ 参数错误");
                return ret; // 参数错误不需要重试
            default:
                SK_LOGE(TAG, "❌ 未知错误: %d", ret);
                break;
        }
        
        // 如果不是最后一次尝试，等待后重试
        if (attempt < max_retries) {
            int delay_ms = 1000 * attempt; // 递增延迟
            SK_LOGI(TAG, "等待 %d ms 后重试...", delay_ms);
            vTaskDelay(pdMS_TO_TICKS(delay_ms));
        }
    }
    
    SK_LOGE(TAG, "❌ 所有重试都失败了");
    return ret;
}
```

### 4. 内存监控示例

```c
/**
 * @brief 内存使用监控示例
 */
void example_memory_monitoring(void) {
    // 记录初始内存状态
    size_t initial_free_heap = heap_caps_get_free_size(MALLOC_CAP_8BIT);
    size_t initial_free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    
    SK_LOGI(TAG, "=== 内存监控开始 ===");
    SK_LOGI(TAG, "初始内存状态:");
    SK_LOGI(TAG, "  - 内部RAM: %zu bytes", initial_free_heap);
    SK_LOGI(TAG, "  - PSRAM: %zu bytes", initial_free_psram);
    
    SkHttpDownloadData downloadData;
    sk_err_t ret = SkHttpDownloadFile("http://example.com/test.opus", &downloadData);
    
    if (ret == SK_RET_SUCCESS) {
        // 检查下载后的内存使用
        size_t after_download_heap = heap_caps_get_free_size(MALLOC_CAP_8BIT);
        size_t after_download_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
        
        SK_LOGI(TAG, "下载后内存状态:");
        SK_LOGI(TAG, "  - 内部RAM: %zu bytes (减少 %zu)", 
               after_download_heap, initial_free_heap - after_download_heap);
        SK_LOGI(TAG, "  - PSRAM: %zu bytes (减少 %zu)", 
               after_download_psram, initial_free_psram - after_download_psram);
        SK_LOGI(TAG, "  - 下载文件大小: %zu bytes", downloadData.totalSize);
        
        // 播放音频
        SkOpusQueueStreamPlay(&downloadData);
        
        // 清理资源
        SkOggResetParser();
        SkHttpDownloadFree(&downloadData);
    }
    
    // 检查清理后的内存状态
    size_t final_free_heap = heap_caps_get_free_size(MALLOC_CAP_8BIT);
    size_t final_free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    
    SK_LOGI(TAG, "清理后内存状态:");
    SK_LOGI(TAG, "  - 内部RAM: %zu bytes", final_free_heap);
    SK_LOGI(TAG, "  - PSRAM: %zu bytes", final_free_psram);
    
    // 检查是否有内存泄漏
    if (final_free_heap < initial_free_heap - 100) { // 允许100字节误差
        SK_LOGW(TAG, "⚠️ 可能存在内部RAM泄漏: %zu bytes", 
               initial_free_heap - final_free_heap);
    }
    
    if (final_free_psram < initial_free_psram - 100) {
        SK_LOGW(TAG, "⚠️ 可能存在PSRAM泄漏: %zu bytes", 
               initial_free_psram - final_free_psram);
    }
    
    if (final_free_heap >= initial_free_heap - 100 && 
        final_free_psram >= initial_free_psram - 100) {
        SK_LOGI(TAG, "✅ 内存清理正常，无泄漏");
    }
    
    SK_LOGI(TAG, "=== 内存监控结束 ===");
}
```

## 测试用例

### 1. 基本功能测试

```c
#include "unity.h"

void test_http_download_success(void) {
    SkHttpDownloadData data;
    sk_err_t ret = SkHttpDownloadFile("http://httpbin.org/bytes/1024", &data);
    
    TEST_ASSERT_EQUAL(SK_RET_SUCCESS, ret);
    TEST_ASSERT_EQUAL(1024, data.totalSize);
    TEST_ASSERT_NOT_NULL(data.ringbuf);
    TEST_ASSERT_NOT_NULL(data.storage);
    
    SkHttpDownloadFree(&data);
}

void test_http_download_invalid_url(void) {
    SkHttpDownloadData data;
    sk_err_t ret = SkHttpDownloadFile("invalid_url", &data);
    
    TEST_ASSERT_NOT_EQUAL(SK_RET_SUCCESS, ret);
}

void test_http_download_null_params(void) {
    SkHttpDownloadData data;
    
    // 测试NULL URL
    sk_err_t ret = SkHttpDownloadFile(NULL, &data);
    TEST_ASSERT_EQUAL(SK_RET_INVALID_PARAM, ret);
    
    // 测试NULL data
    ret = SkHttpDownloadFile("http://example.com", NULL);
    TEST_ASSERT_EQUAL(SK_RET_INVALID_PARAM, ret);
}
```

### 2. OGG解析测试

```c
void test_ogg_parse_valid_opus(void) {
    // 这里需要一个有效的OPUS文件URL
    SkHttpDownloadData data;
    sk_err_t ret = SkHttpDownloadFile("http://example.com/valid.opus", &data);
    
    if (ret == SK_RET_SUCCESS) {
        SkOpusHeader header;
        ret = SkOggParseOpusHeader(data.ringbuf, &header);
        
        TEST_ASSERT_EQUAL(SK_RET_SUCCESS, ret);
        TEST_ASSERT_GREATER_THAN(0, header.channels);
        TEST_ASSERT_GREATER_THAN(0, header.sampleRate);
        
        SkHttpDownloadFree(&data);
    }
}

void test_opus_packet_extraction(void) {
    SkHttpDownloadData data;
    sk_err_t ret = SkHttpDownloadFile("http://example.com/test.opus", &data);
    
    if (ret == SK_RET_SUCCESS) {
        SkOpusPacket packet;
        int packet_count = 0;
        
        while (SkOggGetNextOpusPacket(data.ringbuf, &packet) == SK_RET_SUCCESS) {
            TEST_ASSERT_NOT_NULL(packet.data);
            TEST_ASSERT_GREATER_THAN(0, packet.size);
            
            SkOggFreeOpusPacket(&packet);
            packet_count++;
            
            if (packet_count >= 5) break; // 只测试前5个包
        }
        
        TEST_ASSERT_GREATER_THAN(0, packet_count);
        
        SkOggResetParser();
        SkHttpDownloadFree(&data);
    }
}
```

### 3. 压力测试

```c
void test_multiple_downloads(void) {
    const int test_count = 5;

    for (int i = 0; i < test_count; i++) {
        SkHttpDownloadData data;
        sk_err_t ret = SkHttpDownloadFile("http://httpbin.org/bytes/1024", &data);

        TEST_ASSERT_EQUAL(SK_RET_SUCCESS, ret);
        TEST_ASSERT_EQUAL(1024, data.totalSize);

        SkHttpDownloadFree(&data);

        // 短暂延迟
        vTaskDelay(pdMS_TO_TICKS(100));
    }
}

void test_large_file_download(void) {
    SkHttpDownloadData data;
    // 测试较大文件（但小于2MB限制）
    sk_err_t ret = SkHttpDownloadFile("http://httpbin.org/bytes/1048576", &data); // 1MB

    TEST_ASSERT_EQUAL(SK_RET_SUCCESS, ret);
    TEST_ASSERT_EQUAL(1048576, data.totalSize);

    SkHttpDownloadFree(&data);
}
```

## 调试和故障排除

### 1. 常见问题诊断

```c
/**
 * @brief 诊断网络连接问题
 */
void diagnose_network_issues(void) {
    SK_LOGI(TAG, "=== 网络诊断开始 ===");

    // 检查WiFi连接状态
    // 这里需要根据你的WiFi模块实现
    // wifi_ap_record_t ap_info;
    // esp_err_t ret = esp_wifi_sta_get_ap_info(&ap_info);
    // if (ret == ESP_OK) {
    //     SK_LOGI(TAG, "WiFi已连接: %s, RSSI: %d", ap_info.ssid, ap_info.rssi);
    // } else {
    //     SK_LOGE(TAG, "WiFi未连接");
    //     return;
    // }

    // 测试基本HTTP连接
    SkHttpDownloadData data;
    sk_err_t download_ret = SkHttpDownloadFile("http://httpbin.org/status/200", &data);

    if (download_ret == SK_RET_SUCCESS) {
        SK_LOGI(TAG, "✅ HTTP连接正常");
        SkHttpDownloadFree(&data);
    } else {
        SK_LOGE(TAG, "❌ HTTP连接失败: %d", download_ret);
    }

    SK_LOGI(TAG, "=== 网络诊断结束 ===");
}

/**
 * @brief 诊断内存问题
 */
void diagnose_memory_issues(void) {
    SK_LOGI(TAG, "=== 内存诊断开始 ===");

    // 检查可用内存
    size_t free_heap = heap_caps_get_free_size(MALLOC_CAP_8BIT);
    size_t free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);

    SK_LOGI(TAG, "可用内存:");
    SK_LOGI(TAG, "  - 内部RAM: %zu bytes", free_heap);
    SK_LOGI(TAG, "  - PSRAM: %zu bytes", free_psram);

    // 检查PSRAM是否足够
    if (free_psram < (2 * 1024 * 1024 + 1024)) { // 2MB + 1KB余量
        SK_LOGW(TAG, "⚠️ PSRAM可能不足，需要至少2MB+");
    } else {
        SK_LOGI(TAG, "✅ PSRAM充足");
    }

    // 检查内部RAM是否足够
    if (free_heap < 10240) { // 10KB余量
        SK_LOGW(TAG, "⚠️ 内部RAM可能不足");
    } else {
        SK_LOGI(TAG, "✅ 内部RAM充足");
    }

    SK_LOGI(TAG, "=== 内存诊断结束 ===");
}
```

### 2. 性能监控

```c
/**
 * @brief 性能基准测试
 */
void benchmark_download_performance(void) {
    const char *test_urls[] = {
        "http://httpbin.org/bytes/1024",      // 1KB
        "http://httpbin.org/bytes/10240",     // 10KB
        "http://httpbin.org/bytes/102400",    // 100KB
        "http://httpbin.org/bytes/1048576",   // 1MB
    };

    const char *size_names[] = {"1KB", "10KB", "100KB", "1MB"};
    const int test_count = sizeof(test_urls) / sizeof(test_urls[0]);

    SK_LOGI(TAG, "=== 下载性能基准测试 ===");

    for (int i = 0; i < test_count; i++) {
        SkHttpDownloadData data;

        // 记录开始时间
        TickType_t start_time = xTaskGetTickCount();

        sk_err_t ret = SkHttpDownloadFile(test_urls[i], &data);

        // 记录结束时间
        TickType_t end_time = xTaskGetTickCount();
        uint32_t duration_ms = (end_time - start_time) * portTICK_PERIOD_MS;

        if (ret == SK_RET_SUCCESS) {
            float speed_kbps = (float)data.totalSize / duration_ms; // KB/s
            SK_LOGI(TAG, "%s: %zu bytes, %lu ms, %.2f KB/s",
                   size_names[i], data.totalSize, duration_ms, speed_kbps);

            SkHttpDownloadFree(&data);
        } else {
            SK_LOGE(TAG, "%s: 下载失败 (%d)", size_names[i], ret);
        }

        // 短暂延迟
        vTaskDelay(pdMS_TO_TICKS(500));
    }

    SK_LOGI(TAG, "=== 基准测试完成 ===");
}
```

## 最佳实践建议

### 1. 错误处理最佳实践

```c
/**
 * @brief 推荐的错误处理模式
 */
sk_err_t robust_audio_download_and_play(const char *url) {
    SkHttpDownloadData downloadData = {0};
    sk_err_t ret = SK_RET_SUCCESS;
    bool resources_allocated = false;

    // 参数验证
    if (url == NULL || strlen(url) == 0) {
        SK_LOGE(TAG, "无效的URL参数");
        return SK_RET_INVALID_PARAM;
    }

    // 内存检查
    size_t free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    if (free_psram < (2 * 1024 * 1024 + 10240)) {
        SK_LOGE(TAG, "PSRAM不足: %zu bytes", free_psram);
        return SK_RET_NO_MEMORY;
    }

    // 下载文件
    ret = SkHttpDownloadFile(url, &downloadData);
    if (ret != SK_RET_SUCCESS) {
        SK_LOGE(TAG, "下载失败: %d", ret);
        goto cleanup;
    }
    resources_allocated = true;

    // 验证下载结果
    if (downloadData.totalSize == 0) {
        SK_LOGE(TAG, "下载的文件为空");
        ret = SK_RET_FAIL;
        goto cleanup;
    }

    // 播放音频
    ret = SkOpusQueueStreamPlay(&downloadData);
    if (ret != SK_RET_SUCCESS) {
        SK_LOGE(TAG, "播放失败: %d", ret);
        goto cleanup;
    }

    SK_LOGI(TAG, "音频播放完成");

cleanup:
    // 确保资源正确清理
    if (resources_allocated) {
        SkOggResetParser();
        SkHttpDownloadFree(&downloadData);
    }

    return ret;
}
```

### 2. 资源管理最佳实践

```c
/**
 * @brief RAII风格的资源管理
 */
typedef struct {
    SkHttpDownloadData *download_data;
    bool parser_initialized;
} AudioResourceManager;

static void audio_resource_cleanup(AudioResourceManager *mgr) {
    if (mgr == NULL) return;

    if (mgr->parser_initialized) {
        SkOggResetParser();
        mgr->parser_initialized = false;
    }

    if (mgr->download_data) {
        SkHttpDownloadFree(mgr->download_data);
        mgr->download_data = NULL;
    }
}

sk_err_t safe_audio_processing(const char *url) {
    AudioResourceManager mgr = {0};
    SkHttpDownloadData downloadData;
    sk_err_t ret;

    // 下载
    ret = SkHttpDownloadFile(url, &downloadData);
    if (ret != SK_RET_SUCCESS) {
        return ret;
    }

    mgr.download_data = &downloadData;
    mgr.parser_initialized = true;

    // 处理音频...
    ret = SkOpusQueueStreamPlay(&downloadData);

    // 自动清理
    audio_resource_cleanup(&mgr);

    return ret;
}
```

## 总结

这个示例集合提供了SK HTTP Downloader模块的全面使用指南，包括：

🔧 **完整示例** - 从基础到高级的使用模式
🧪 **测试用例** - 单元测试和集成测试示例
🔍 **调试工具** - 网络和内存诊断功能
📊 **性能监控** - 基准测试和性能分析
✅ **最佳实践** - 错误处理和资源管理模式

通过这些示例，开发者可以：
- 快速上手使用模块
- 正确处理各种异常情况
- 监控和优化性能
- 编写可靠的音频应用

建议在实际项目中根据具体需求调整这些示例代码。
```
