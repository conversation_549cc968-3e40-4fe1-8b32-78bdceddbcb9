/**
 * @file: SK_HTTP_DOWNLOADER_USAGE_EXAMPLE.c
 * @description: 状态机版本HTTP下载器使用示例
 * @author: <PERSON>
 * @date: 2025-01-20
 */

#include "sk_http_downloader.h"
#include "sk_log.h"

static const char *TAG = "HttpDownloaderExample";

// 进度回调函数
void on_download_progress(SkHttpState state, int progress, void *user_data) {
    switch (state) {
        case HTTP_STATE_IDLE:
            SK_LOGI(TAG, "📱 状态：空闲");
            break;
            
        case HTTP_STATE_CONNECTING:
            SK_LOGI(TAG, "🔗 状态：连接服务器...");
            break;
            
        case HTTP_STATE_DOWNLOADING:
            SK_LOGI(TAG, "⬇️ 状态：下载中 %d%%", progress);
            break;
            
        case HTTP_STATE_PARSING:
            SK_LOGI(TAG, "🔍 状态：解析音频文件...");
            break;
            
        case HTTP_STATE_PLAYING:
            SK_LOGI(TAG, "🎵 状态：播放中 %d%%", progress);
            break;
            
        case HTTP_STATE_COMPLETE:
            SK_LOGI(TAG, "✅ 状态：完成");
            break;
            
        case HTTP_STATE_ERROR:
            SK_LOGE(TAG, "❌ 状态：错误");
            break;
            
        default:
            SK_LOGW(TAG, "❓ 未知状态：%d", state);
            break;
    }
}

// 完成回调函数
void on_download_complete(sk_err_t result, void *user_data) {
    if (result == SK_RET_SUCCESS) {
        SK_LOGI(TAG, "🎉 下载播放完成！");
    } else {
        SK_LOGE(TAG, "💥 下载播放失败：错误码 %d", result);
    }
    
    // 可以在这里处理完成后的逻辑
    // 例如：通知UI、启动下一个任务等
}

// 基本使用示例
void example_basic_usage(void) {
    SK_LOGI(TAG, "=== 基本使用示例 ===");
    
    const char *audio_url = "http://example.com/test_audio.opus";
    
    // 启动异步下载播放（一行代码搞定！）
    sk_err_t ret = SkHttpDownloadPlayAsync(
        audio_url,              // 音频URL
        on_download_progress,   // 进度回调
        on_download_complete,   // 完成回调
        NULL                    // 用户数据（可选）
    );
    
    if (ret == SK_RET_SUCCESS) {
        SK_LOGI(TAG, "✅ 异步下载已启动");
        
        // 主任务可以继续做其他事情，不会被阻塞！
        // 下载和播放在后台状态机中进行
        
    } else {
        SK_LOGE(TAG, "❌ 启动失败：%d", ret);
    }
}

// 带用户数据的示例
typedef struct {
    int task_id;
    char task_name[32];
} UserTaskData;

void on_progress_with_userdata(SkHttpState state, int progress, void *user_data) {
    UserTaskData *task = (UserTaskData*)user_data;
    SK_LOGI(TAG, "[任务%d:%s] 状态：%d, 进度：%d%%", 
           task->task_id, task->task_name, state, progress);
}

void on_complete_with_userdata(sk_err_t result, void *user_data) {
    UserTaskData *task = (UserTaskData*)user_data;
    if (result == SK_RET_SUCCESS) {
        SK_LOGI(TAG, "[任务%d:%s] ✅ 完成", task->task_id, task->task_name);
    } else {
        SK_LOGE(TAG, "[任务%d:%s] ❌ 失败：%d", task->task_id, task->task_name, result);
    }
}

void example_with_userdata(void) {
    SK_LOGI(TAG, "=== 带用户数据示例 ===");
    
    // 用户任务数据
    static UserTaskData task_data = {
        .task_id = 1001,
        .task_name = "背景音乐"
    };
    
    sk_err_t ret = SkHttpDownloadPlayAsync(
        "http://example.com/background_music.opus",
        on_progress_with_userdata,
        on_complete_with_userdata,
        &task_data  // 传递用户数据
    );
    
    if (ret == SK_RET_SUCCESS) {
        SK_LOGI(TAG, "任务 %d 已启动", task_data.task_id);
    }
}

// 控制示例（停止下载）
void example_control(void) {
    SK_LOGI(TAG, "=== 控制示例 ===");
    
    // 启动下载
    sk_err_t ret = SkHttpDownloadPlayAsync(
        "http://example.com/long_audio.opus",
        on_download_progress,
        on_download_complete,
        NULL
    );
    
    if (ret == SK_RET_SUCCESS) {
        SK_LOGI(TAG, "下载已启动");
        
        // 模拟：5秒后停止下载
        vTaskDelay(pdMS_TO_TICKS(5000));
        
        // 检查当前状态
        SkHttpState current_state = SkHttpDownloadGetState();
        SK_LOGI(TAG, "当前状态：%d", current_state);
        
        // 停止下载
        ret = SkHttpDownloadStop();
        if (ret == SK_RET_SUCCESS) {
            SK_LOGI(TAG, "⏹️ 下载已停止");
        } else {
            SK_LOGW(TAG, "停止失败：%d", ret);
        }
    }
}

// 错误处理示例
void example_error_handling(void) {
    SK_LOGI(TAG, "=== 错误处理示例 ===");
    
    // 测试无效URL
    sk_err_t ret = SkHttpDownloadPlayAsync(
        "",  // 空URL
        on_download_progress,
        on_download_complete,
        NULL
    );
    
    if (ret != SK_RET_SUCCESS) {
        SK_LOGI(TAG, "✅ 正确检测到无效参数：%d", ret);
    }
    
    // 测试重复启动
    ret = SkHttpDownloadPlayAsync(
        "http://example.com/test1.opus",
        on_download_progress,
        on_download_complete,
        NULL
    );
    
    if (ret == SK_RET_SUCCESS) {
        // 立即尝试启动第二个任务
        ret = SkHttpDownloadPlayAsync(
            "http://example.com/test2.opus",
            on_download_progress,
            on_download_complete,
            NULL
        );
        
        if (ret == SK_RET_INVALID_OPERATION) {
            SK_LOGI(TAG, "✅ 正确检测到重复启动：%d", ret);
        }
        
        // 清理第一个任务
        SkHttpDownloadStop();
    }
}

// 主应用示例
void app_main_example(void) {
    SK_LOGI(TAG, "=== 主应用示例 ===");
    
    // 初始化其他系统...
    
    // 启动音频下载播放（非阻塞）
    SkHttpDownloadPlayAsync(
        "http://example.com/startup_sound.opus",
        on_download_progress,
        on_download_complete,
        NULL
    );
    
    // 主任务继续处理其他事情
    while (1) {
        // 处理按键、网络、传感器等
        SK_LOGI(TAG, "主任务正在处理其他事情...");
        
        // 检查下载状态（可选）
        SkHttpState state = SkHttpDownloadGetState();
        if (state == HTTP_STATE_ERROR) {
            SK_LOGW(TAG, "检测到下载错误，可能需要重试");
        }
        
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
}

// 内存使用对比示例
void example_memory_comparison(void) {
    SK_LOGI(TAG, "=== 内存使用对比 ===");
    
    // 记录启动前的内存状态
    size_t free_heap_before = heap_caps_get_free_size(MALLOC_CAP_8BIT);
    size_t free_psram_before = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    
    SK_LOGI(TAG, "启动前内存：");
    SK_LOGI(TAG, "  内部RAM: %zu bytes", free_heap_before);
    SK_LOGI(TAG, "  PSRAM: %zu bytes", free_psram_before);
    
    // 启动下载
    sk_err_t ret = SkHttpDownloadPlayAsync(
        "http://example.com/memory_test.opus",
        on_download_progress,
        on_download_complete,
        NULL
    );
    
    if (ret == SK_RET_SUCCESS) {
        // 等待一段时间让任务启动
        vTaskDelay(pdMS_TO_TICKS(1000));
        
        // 检查启动后的内存使用
        size_t free_heap_after = heap_caps_get_free_size(MALLOC_CAP_8BIT);
        size_t free_psram_after = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
        
        SK_LOGI(TAG, "启动后内存：");
        SK_LOGI(TAG, "  内部RAM: %zu bytes (减少 %zu)", 
               free_heap_after, free_heap_before - free_heap_after);
        SK_LOGI(TAG, "  PSRAM: %zu bytes (减少 %zu)", 
               free_psram_after, free_psram_before - free_psram_after);
        
        SK_LOGI(TAG, "💡 状态机版本大大减少了内存使用！");
    }
}

// 性能测试示例
void example_performance_test(void) {
    SK_LOGI(TAG, "=== 性能测试示例 ===");
    
    const char *test_urls[] = {
        "http://example.com/small.opus",   // 小文件
        "http://example.com/medium.opus",  // 中等文件
        "http://example.com/large.opus"    // 大文件
    };
    
    for (int i = 0; i < 3; i++) {
        SK_LOGI(TAG, "测试文件 %d: %s", i + 1, test_urls[i]);
        
        // 记录开始时间
        TickType_t start_time = xTaskGetTickCount();
        
        // 启动下载（这里只是演示，实际需要等待完成）
        sk_err_t ret = SkHttpDownloadPlayAsync(
            test_urls[i],
            on_download_progress,
            on_download_complete,
            NULL
        );
        
        if (ret == SK_RET_SUCCESS) {
            TickType_t end_time = xTaskGetTickCount();
            uint32_t startup_time = (end_time - start_time) * portTICK_PERIOD_MS;
            
            SK_LOGI(TAG, "启动时间: %lu ms (非常快！)", startup_time);
            
            // 停止当前任务，准备下一个测试
            SkHttpDownloadStop();
            vTaskDelay(pdMS_TO_TICKS(100));
        }
    }
}
