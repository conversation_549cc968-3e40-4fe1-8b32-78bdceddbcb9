# SK HTTP Downloader 模块完整说明文档

## 概述

SK HTTP Downloader 是一个功能完整的HTTP音频文件下载和播放模块，专门为ESP32 PSRAM环境设计。该模块不仅提供HTTP文件下载功能，还集成了OGG/OPUS音频文件解析和实时播放能力。

## 模块架构

该模块由三个核心功能模块组成：

### 1. HTTP下载模块
- **PSRAM存储**：所有数据存储在PSRAM中，节省内部RAM
- **静态环形缓冲区**：使用`xRingbufferCreateStatic`创建环形缓冲区
- **一次性内存分配**：数据存储空间和结构体空间在一次分配中完成
- **完整错误处理**：包含网络错误、内存分配失败等各种异常处理

### 2. OGG/OPUS解析模块
- **OGG容器解析**：完整解析OGG文件格式，提取OPUS音频包
- **OPUS头部解析**：提取音频参数（采样率、声道数等）
- **流式包提取**：逐个提取OPUS音频包，支持大文件处理
- **状态管理**：维护解析状态，支持重置和重新开始

### 3. 队列集成播放模块
- **实时播放**：直接播放下载的OPUS音频文件
- **解码集成**：使用现有OPUS解码器进行音频解码
- **音频队列**：利用现有音频系统进行播放
- **性能优化**：静态缓冲区和批量处理优化

## 文件结构

```
main/
├── include/
│   └── sk_http_downloader.h    # 头文件 (109行)
└── network/
    └── sk_http_downloader.c    # 实现文件 (568行)
```

## 数据结构定义

### 1. 下载数据结构
```c
typedef struct {
    RingbufHandle_t ringbuf;    // 环形缓冲区句柄
    uint8_t *storage;           // PSRAM存储指针
    size_t totalSize;           // 文件总大小
} SkHttpDownloadData;
```

### 2. OPUS头部结构
```c
typedef struct {
    uint8_t version;        // OPUS版本
    uint8_t channels;       // 声道数
    uint16_t preSkip;       // 预跳过样本数
    uint32_t sampleRate;    // 采样率
    uint16_t outputGain;    // 输出增益
} SkOpusHeader;
```

### 3. OPUS音频包结构
```c
typedef struct {
    uint8_t *data;          // OPUS包数据
    size_t size;            // 包大小
} SkOpusPacket;
```

## API 接口详解

### HTTP下载接口

#### 1. SkHttpDownloadFile()
```c
sk_err_t SkHttpDownloadFile(const char *url, SkHttpDownloadData *downloadData);
```

**功能**：下载文件到PSRAM环形缓冲区

**参数**：
- `url`：文件URL地址
- `downloadData`：输出的下载数据结构

**返回值**：
- `SK_RET_SUCCESS`：下载成功
- `SK_RET_INVALID_PARAM`：参数无效
- `SK_RET_FAIL`：网络连接失败或文件大小无效
- `SK_RET_NO_MEMORY`：内存分配失败

**实现特点**：
- 支持最大2MB文件下载
- 实时显示下载进度
- 自动验证文件完整性
- 完整的错误处理和资源清理

#### 2. SkHttpDownloadFree()
```c
void SkHttpDownloadFree(SkHttpDownloadData *downloadData);
```

**功能**：释放下载资源

**参数**：
- `downloadData`：要释放的下载数据结构

**实现特点**：
- 自动释放环形缓冲区
- 释放PSRAM存储空间
- 重置所有状态变量

### OGG/OPUS解析接口

#### 3. SkOggParseOpusHeader()
```c
sk_err_t SkOggParseOpusHeader(RingbufHandle_t ringbuf, SkOpusHeader *header);
```

**功能**：从环形缓冲区解析OPUS头部信息

**参数**：
- `ringbuf`：环形缓冲区句柄
- `header`：输出OPUS头部信息

**返回值**：
- `SK_RET_SUCCESS`：解析成功
- `SK_RET_INVALID_PARAM`：参数无效
- `SK_RET_FAIL`：文件格式错误
- `SK_RET_TIMEOUT`：数据获取超时

#### 4. SkOggGetNextOpusPacket()
```c
sk_err_t SkOggGetNextOpusPacket(RingbufHandle_t ringbuf, SkOpusPacket *packet);
```

**功能**：从环形缓冲区提取下一个OPUS音频包

**参数**：
- `ringbuf`：环形缓冲区句柄
- `packet`：输出OPUS包

**返回值**：
- `SK_RET_SUCCESS`：提取成功
- `SK_RET_NOT_FOUND`：没有更多包
- `SK_RET_NO_MEMORY`：内存分配失败
- `SK_RET_FAIL`：解析错误

#### 5. SkOggFreeOpusPacket()
```c
void SkOggFreeOpusPacket(SkOpusPacket *packet);
```

**功能**：释放OPUS包资源

#### 6. SkOggResetParser() / SkOggResetParserPosition()
```c
void SkOggResetParser(void);           // 完全重置（释放数据）
void SkOggResetParserPosition(void);   // 重置位置（保留数据）
```

**功能**：重置OGG解析状态

### 队列集成播放接口

#### 7. SkOpusQueueStreamPlay()
```c
sk_err_t SkOpusQueueStreamPlay(SkHttpDownloadData *downloadData);
```

**功能**：队列集成OPUS流式播放

**参数**：
- `downloadData`：下载的数据

**返回值**：
- `SK_RET_SUCCESS`：播放成功
- `SK_RET_FAIL`：播放失败

**实现特点**：
- 自动启动/停止扬声器
- 实时解码和播放
- 性能优化（静态缓冲区）
- 安全限制（最多2000包）
- 详细的播放统计信息

## 使用示例

### 1. 基本HTTP下载

```c
#include "sk_http_downloader.h"

void example_basic_download(void) {
    SkHttpDownloadData downloadData;

    // 下载文件
    sk_err_t ret = SkHttpDownloadFile("http://example.com/music.opus", &downloadData);
    if (ret == SK_RET_SUCCESS) {
        SK_LOGI(TAG, "Downloaded %zu bytes to PSRAM", downloadData.totalSize);

        // 释放资源
        SkHttpDownloadFree(&downloadData);
    } else {
        SK_LOGE(TAG, "Download failed: %d", ret);
    }
}
```

### 2. OPUS音频解析

```c
void example_opus_parsing(void) {
    SkHttpDownloadData downloadData;

    // 下载OPUS文件
    if (SkHttpDownloadFile("http://example.com/audio.opus", &downloadData) == SK_RET_SUCCESS) {

        // 解析OPUS头部
        SkOpusHeader header;
        if (SkOggParseOpusHeader(downloadData.ringbuf, &header) == SK_RET_SUCCESS) {
            SK_LOGI(TAG, "OPUS: %dch, %luHz, preSkip:%d",
                   header.channels, header.sampleRate, header.preSkip);
        }

        // 逐个提取OPUS包
        SkOpusPacket packet;
        int packetCount = 0;
        while (SkOggGetNextOpusPacket(downloadData.ringbuf, &packet) == SK_RET_SUCCESS) {
            packetCount++;
            SK_LOGI(TAG, "Packet #%d: %zu bytes", packetCount, packet.size);

            // 处理音频包...

            SkOggFreeOpusPacket(&packet);
        }

        // 清理资源
        SkOggResetParser();
        SkHttpDownloadFree(&downloadData);
    }
}
```

### 3. 一键下载播放

```c
void example_download_and_play(void) {
    SkHttpDownloadData downloadData;

    // 下载OPUS音频文件
    sk_err_t ret = SkHttpDownloadFile("http://example.com/music.opus", &downloadData);
    if (ret == SK_RET_SUCCESS) {
        SK_LOGI(TAG, "Download completed, starting playback...");

        // 直接播放
        ret = SkOpusQueueStreamPlay(&downloadData);
        if (ret == SK_RET_SUCCESS) {
            SK_LOGI(TAG, "Playback completed successfully");
        } else {
            SK_LOGE(TAG, "Playback failed: %d", ret);
        }

        // 清理资源
        SkOggResetParser();
        SkHttpDownloadFree(&downloadData);
    }
}
```

## 配置参数

在 `sk_http_downloader.c` 中可以调整以下参数：

```c
#define HTTP_BUFFER_SIZE        4096                    // HTTP读取缓冲区大小
#define HTTP_TIMEOUT_MS         30000                   // HTTP超时时间(30秒)
#define RINGBUF_SIZE            (2 * 1024 * 1024)      // 环形缓冲区大小(2MB)
```

## 内存管理详解

### PSRAM分配策略

1. **一次性分配**：`RINGBUF_SIZE + sizeof(StaticRingbuffer_t)` 字节
2. **内存布局**：
   ```
   [数据存储空间: 2MB] [结构体空间: ~几百字节]
   ```
3. **自动清理**：调用 `SkHttpDownloadFree()` 自动释放所有PSRAM内存

### 环形缓冲区管理

- 使用 `xRingbufferCreateStatic()` 创建静态环形缓冲区
- 数据存储和结构体都在PSRAM中
- 支持多线程安全访问
- 自动处理数据分片和重组

### OGG解析内存管理

- 全局静态变量管理解析状态
- 一次性加载所有音频数据到内存
- 支持重置和重新开始解析
- 自动释放OPUS包内存

## 错误处理机制

### HTTP下载错误处理
1. **参数检查**：检查URL和输出参数有效性
2. **网络错误**：HTTP连接失败、读取错误等
3. **内存错误**：PSRAM分配失败、环形缓冲区创建失败
4. **文件大小检查**：超过最大文件大小限制
5. **资源清理**：任何错误情况下都会正确清理已分配的资源

### OGG解析错误处理
1. **格式验证**：检查OGG和OPUS头部格式
2. **数据完整性**：验证页面和段的完整性
3. **内存保护**：防止缓冲区溢出
4. **状态一致性**：确保解析状态的正确性

### 播放错误处理
1. **解码器检查**：验证OPUS解码器可用性
2. **音频系统**：检查扬声器和音频队列状态
3. **性能保护**：防止看门狗超时
4. **安全限制**：限制最大处理包数

## 性能优化特性

### 内存优化
- PSRAM存储减少内部RAM使用
- 静态缓冲区避免频繁内存分配
- 一次性内存分配减少碎片

### 处理优化
- 流式处理支持大文件
- 批量数据传输
- 智能日志输出减少性能影响

### 播放优化
- 静态PCM缓冲区
- 任务调度优化
- 实时性能监控

## 日志输出系统

模块使用分层日志输出：

- `SK_LOGI`：下载进度、播放状态、完成信息
- `SK_LOGW`：警告信息、超时提示
- `SK_LOGE`：错误信息、失败原因
- **智能日志**：减少重复日志，关键时刻详细输出

## 依赖项和集成

### 核心依赖
- ESP-IDF HTTP客户端 (`esp_http_client`)
- FreeRTOS环形缓冲区 (`freertos/ringbuf`)
- ESP32 PSRAM支持 (`esp_heap_caps`)

### SK项目集成
- SK项目日志系统 (`sk_log`)
- SK OPUS解码器 (`sk_opus_dec`)
- SK音频系统 (`sk_board`)
- SK通用定义 (`sk_common`)

## 注意事项和限制

### 硬件要求
1. **PSRAM要求**：需要ESP32配置并启用PSRAM
2. **网络连接**：需要WiFi连接正常
3. **音频硬件**：需要配置扬声器和音频系统

### 软件限制
1. **文件大小限制**：默认最大2MB，可根据PSRAM大小调整
2. **格式支持**：仅支持OGG/OPUS格式音频文件
3. **并发限制**：同时只能处理一个下载任务

### 使用注意
1. **线程安全**：环形缓冲区操作是线程安全的
2. **资源管理**：必须调用相应的释放函数
3. **状态管理**：注意解析器状态的重置
4. **性能考虑**：大文件下载会占用较多PSRAM

## 代码统计和模块信息

- **头文件**：109行
- **实现文件**：568行
- **总代码量**：677行
- **核心函数**：7个主要API
- **配置宏**：3个
- **数据结构**：3个

### 功能模块分布
- **HTTP下载模块**：~180行 (32%)
- **OGG解析模块**：~250行 (44%)
- **队列播放模块**：~130行 (23%)

这是一个功能完整的HTTP音频下载和播放模块，专为ESP32 PSRAM环境和OPUS音频格式优化。

## 模块工作流程图

上面的流程图展示了SK HTTP Downloader模块的完整工作流程，包括四个主要处理路径：

### 1. HTTP下载流程（蓝色路径）
- 从参数验证开始，建立HTTP连接
- 检查文件大小限制，分配PSRAM内存
- 创建静态环形缓冲区，循环下载数据
- 验证下载完整性，清理资源

### 2. OGG解析流程（紫色路径）
- 初始化OGG解析器，获取环形缓冲区数据
- 验证OGG格式，解析OPUS头部
- 提取音频参数（采样率、声道数等）

### 3. OPUS包提取流程（橙色路径）
- 检查解析器状态，处理首次调用
- 跳过头部和注释页面，解析音频页面
- 逐个提取OPUS音频包，管理解析位置

### 4. 队列播放流程（绿色路径）
- 获取OPUS解码器，初始化解析状态
- 循环获取和解码OPUS包
- 启动扬声器，播放PCM数据
- 统计播放结果，管理资源

### 5. 资源清理流程（灰色路径）
- 释放环形缓冲区和PSRAM内存
- 重置解析状态和音频数据
- 确保所有资源正确释放

## 技术特点总结

### 🚀 性能优化
- **PSRAM存储**：大容量音频文件存储
- **静态缓冲区**：减少内存分配开销
- **流式处理**：支持大文件实时处理
- **批量传输**：优化网络和音频传输效率

### 🛡️ 稳定性保障
- **完整错误处理**：覆盖所有可能的异常情况
- **资源自动清理**：防止内存泄漏
- **状态一致性**：确保解析和播放状态正确
- **安全限制**：防止无限循环和看门狗超时

### 🔧 易用性设计
- **简洁API**：7个主要函数覆盖所有功能
- **一键播放**：下载和播放一体化
- **智能日志**：详细但不冗余的状态信息
- **灵活配置**：可调整的缓冲区和超时参数

### 🎵 音频专业性
- **OPUS格式支持**：高质量音频压缩格式
- **OGG容器解析**：完整的容器格式支持
- **实时解码播放**：低延迟音频处理
- **音频系统集成**：与现有音频框架无缝集成

这个模块是ESP32音频应用开发的理想选择，特别适合需要网络音频下载和播放功能的项目。

## 模块架构图说明

上面的架构图展示了SK HTTP Downloader模块的分层设计：

### 📱 应用层 (Application Layer)
- **应用程序**：用户的具体应用逻辑
- **API接口层**：提供统一的函数接口

### 🔧 SK HTTP Downloader 核心模块
分为四个功能子模块：

#### 🌐 HTTP下载模块 (紫色)
- **HTTP客户端**：管理HTTP连接和请求
- **下载管理器**：控制下载流程和数据传输
- **进度监控**：实时监控下载进度

#### 🎵 OGG/OPUS解析模块 (橙色)
- **OGG容器解析**：解析OGG文件格式
- **OPUS头部解析**：提取音频参数信息
- **音频包提取**：逐个提取OPUS音频包

#### 🔊 队列播放模块 (绿色)
- **播放队列管理**：管理音频播放队列
- **OPUS解码适配**：适配现有解码器接口
- **音频播放控制**：控制扬声器播放

#### 💾 内存管理模块 (粉色)
- **PSRAM分配器**：管理PSRAM内存分配
- **环形缓冲区**：提供高效的数据缓冲
- **资源清理**：确保资源正确释放

### ⚙️ 系统依赖层 (System Dependencies)
#### ESP-IDF框架
- **esp_http_client**：HTTP客户端库
- **esp_heap_caps**：内存管理库
- **FreeRTOS RingBuffer**：环形缓冲区库

#### SK项目框架
- **sk_log**：日志系统
- **sk_opus_dec**：OPUS解码器
- **sk_board**：硬件抽象层
- **sk_common**：通用定义

### 🔌 硬件层 (Hardware Layer)
- **WiFi网络**：网络连接硬件
- **PSRAM硬件**：外部RAM存储
- **扬声器硬件**：音频输出设备

## 设计优势

### 🏗️ 分层架构
- **清晰的职责分离**：每层专注特定功能
- **良好的可维护性**：模块化设计便于维护
- **灵活的扩展性**：易于添加新功能

### 🔄 数据流设计
- **单向数据流**：从网络到PSRAM到播放
- **缓冲区优化**：环形缓冲区提供高效数据传输
- **内存复用**：最小化内存分配和释放

### 🛠️ 接口设计
- **统一API**：简洁一致的函数接口
- **错误处理**：完整的错误码和异常处理
- **状态管理**：清晰的状态转换和管理

这个架构设计确保了模块的高性能、高可靠性和易用性，是ESP32音频应用的优秀解决方案。
