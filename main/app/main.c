/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: main.c
 * @description: 程序入口
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <stdlib.h>
#include <stdio.h>
#include "sdkconfig.h"
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <errno.h>
#include <netdb.h>            // struct addrinfo
#include <arpa/inet.h>
#include "sk_os.h"
#include "sk_board.h"
#include "sk_audio.h"
#include "sk_clink.h"
#include "sk_rlink.h"
#include "sk_sm.h"
#include "sk_config.h"
#include "sk_wifi.h"
#include "sk_opus.h"
#include "sk_opus_dec.h"
#include "sk_opus_enc.h"
#include "sk_dfx.h"
#include "sk_log.h"
#include "sk_websocket.h"
#include "sk_test.h"
#include "sk_ota_api.h"
#include "sk_rlink.h"
#include "sk_http_downloader.h"

#define RESERVE_MEM_SIZE_PER_BLOCK (2048)

static const char *TAG = "SmartKid";

SkSpeechMapItem g_skSpeechMap[] = {
    {SPEECH_CMD_EVENT_CHAT, "wu kong"},
    {SPEECH_CMD_EVENT_MUSIC, "ying yue"},
    {SPEECH_CMD_EVENT_CONFIG, "pei zhi"},
    {SPEECH_CMD_EVENT_CONFIG, "pei zi"},
    {SPEECH_CMD_EVENT_CONFIG, "she zhi"},
    {SPEECH_CMD_EVENT_QUERY, "cha cha"},
    {SPEECH_CMD_EVENT_VOLUP, "sheng ying da yi dian"},
    {SPEECH_CMD_EVENT_VOLUP, "tiao gao ying liang"},
    {SPEECH_CMD_EVENT_VOLUP, "tiao da ying liang"},
    {SPEECH_CMD_EVENT_VOLUP, "da sheng yi dian"},
    {SPEECH_CMD_EVENT_VOLUP, "da sheng dian"},
    {SPEECH_CMD_EVENT_VOLUP, "zai da yi dian"},
    {SPEECH_CMD_EVENT_VOLDOWN, "sheng ying xiao yi dian"},
    {SPEECH_CMD_EVENT_VOLDOWN, "tiao di ying liang"},
    {SPEECH_CMD_EVENT_VOLDOWN, "tiao xiao ying liang"},
    {SPEECH_CMD_EVENT_VOLDOWN, "xiao sheng yi dian"},
    {SPEECH_CMD_EVENT_VOLDOWN, "xiao sheng dian"},
    {SPEECH_CMD_EVENT_VOLDOWN, "zai xiao yi dian"},
    {SPEECH_CMD_EVENT_VOLMAX, "zui da ying liang"},
    {SPEECH_CMD_EVENT_VOLMAX, "zui da sheng"},
    {SPEECH_CMD_EVENT_VOLMIN, "zui xiao yi dian"},
    {SPEECH_CMD_EVENT_VOLMIN, "zui xiao sheng"},
    {SPEECH_CMD_EVENT_HELP, "qiu zhu"},
    {SPEECH_CMD_EVENT_PAUSE, "zan ting"},
    {SPEECH_CMD_EVENT_CONFIRM, "que ding"},
    {SPEECH_CMD_EVENT_QUIT, "tui chu"},
    {SPEECH_CMD_EVENT_QUIT, "ting zhi"},
    {SPEECH_CMD_EVENT_PREV, "shang yi ge"},
    {SPEECH_CMD_EVENT_NEXT, "xia yi ge"},
    {SPEECH_CMD_EVENT_RESUME, "ji xu"},
    {SPEECH_CMD_EVENT_QUIT, "qu xiao"},
    {SPEECH_CMD_EVENT_INFO, "zhuang tai"},
    {SPEECH_CMD_EVENT_START_DBG, "qi dong"},
    {SPEECH_CMD_EVENT_STOP_DBG, "duan kai"},
    {SPEECH_CMD_EVENT_SLEEP, "dai ji"},
    {SPEECH_CMD_EVENT_CALL, "fu jiao"},
    {SPEECH_CMD_EVENT_CALL, "hu jiao"},
    {SPEECH_CMD_EVENT_MIC_ON, "lu ying"},
    {SPEECH_CMD_EVENT_MIC_ON, "lu yin"},
    {SPEECH_CMD_EVENT_MIC_OFF, "guan bi"},
    {SPEECH_CMD_EVENT_CALL, "qi dong hu jiao"},
    {SPEECH_CMD_EVENT_CALL, "da kai dian hua"},
    {SPEECH_CMD_EVENT_CONFIG, "pei wang"},
};

SkStateHandler g_smHandler;

void SkMainCmdProc(int32_t cmd) {
    if (g_smHandler == NULL) {
        return;
    }

    SkSmSendEvent(g_smHandler, SM_EVENT_CMD, cmd, 0, 0);
    return;
}

// 函数声明
void TestOggParsing(SkHttpDownloadData *downloadData);

// HTTP下载测试函数
void TestHttpDownload(void) {
    SK_LOGI(TAG, "Testing HTTP download...");

    SkHttpDownloadData downloadData;
    const char *testUrl = "http://************:8070/Desktop/music.opus";

    sk_err_t ret = SkHttpDownloadFile(testUrl, &downloadData);

    if (ret == SK_RET_SUCCESS) {
        SK_LOGI(TAG, "HTTP download success! Size: %zu bytes", downloadData.totalSize);

        // 测试OGG解析功能
        TestOggParsing(&downloadData);

        // 队列集成OPUS流式播放（利用现有音频系统）
        SK_LOGI(TAG, "");
        sk_err_t playRet = SkOpusQueueStreamPlay(&downloadData);
        if (playRet == SK_RET_SUCCESS) {
            SK_LOGI(TAG, "✅ Queue stream play started successfully!");
            SK_LOGI(TAG, "🎵 Audio will play via existing SkPlayerTask");

            // 等待一段时间让音频播放
            SK_LOGI(TAG, "⏳ Waiting for audio playback...");
            vTaskDelay(pdMS_TO_TICKS(5000)); // 等待5秒
        } else {
            SK_LOGE(TAG, "❌ Queue stream play failed: %d", playRet);
        }

        // 释放资源
        SkHttpDownloadFree(&downloadData);
        SK_LOGI(TAG, "Resources freed");

    } else {
        SK_LOGE(TAG, "HTTP download failed: %d", ret);
    }
}

// OGG解析测试函数
void TestOggParsing(SkHttpDownloadData *downloadData) {
    SK_LOGI(TAG, "=== Testing OGG Parsing ===");

    // 1. 解析OPUS头部信息
    SkOpusHeader header;
    sk_err_t ret = SkOggParseOpusHeader(downloadData->ringbuf, &header);
    if (ret != SK_RET_SUCCESS) {
        SK_LOGE(TAG, "Failed to parse OPUS header: %d", ret);
        return;
    }

    SK_LOGI(TAG, "OPUS Header:");
    SK_LOGI(TAG, "   Version: %d, Channels: %d", header.version, header.channels);
    SK_LOGI(TAG, "   Sample Rate: %lu Hz, Pre-skip: %d", header.sampleRate, header.preSkip);

    // 2. 提取所有音频数据包
    SK_LOGI(TAG, "--- Extracting ALL OPUS packets ---");
    int packetCount = 0;
    size_t totalBytes = 0;
    size_t minPacketSize = SIZE_MAX;
    size_t maxPacketSize = 0;

    while (true) {
        SkOpusPacket packet;
        ret = SkOggGetNextOpusPacket(downloadData->ringbuf, &packet);

        if (ret == SK_RET_SUCCESS) {
            packetCount++;
            totalBytes += packet.size;

            // 更新包大小统计
            if (packet.size < minPacketSize) minPacketSize = packet.size;
            if (packet.size > maxPacketSize) maxPacketSize = packet.size;

            // 只显示关键节点的信息，大幅减少日志输出
            if (packetCount <= 3 || packetCount % 100 == 0) {
                SK_LOGI(TAG, "   Packet %d: %zu bytes", packetCount, packet.size);

                // 只在前3个包显示数据内容
                if (packetCount <= 3 && packet.size >= 4) {
                    SK_LOGI(TAG, "     Data: %02X %02X %02X %02X...",
                           packet.data[0], packet.data[1], packet.data[2], packet.data[3]);
                }
            }

            SkOggFreeOpusPacket(&packet);

        } else if (ret == SK_RET_NOT_FOUND) {
            SK_LOGI(TAG, "   Reached end of audio data");
            break;
        } else {
            SK_LOGE(TAG, "   Error extracting packet %d: %d", packetCount + 1, ret);
            break;
        }

        // 防止无限循环的安全检查
        if (packetCount > 1000) {
            SK_LOGW(TAG, "   ⚠️ Stopped at 1000 packets (safety limit)");
            break;
        }

        // 每处理10个包让出CPU时间，防止看门狗超时
        if (packetCount % 10 == 0) {
            vTaskDelay(pdMS_TO_TICKS(1));
        }
    }

    // 3. 详细统计信息
    SK_LOGI(TAG, "=== Complete Analysis ===");
    SK_LOGI(TAG, " Total packets extracted: %d", packetCount);
    SK_LOGI(TAG, "Total audio data: %zu bytes", totalBytes);

    if (packetCount > 0) {
        SK_LOGI(TAG, "  Packet size statistics:");
        SK_LOGI(TAG, "   Average: %zu bytes", totalBytes / packetCount);
        SK_LOGI(TAG, "   Minimum: %zu bytes", minPacketSize);
        SK_LOGI(TAG, "   Maximum: %zu bytes", maxPacketSize);

        // 估算音频时长（16kHz单声道，每包约20ms）
        float estimatedDuration = packetCount * 0.02f; // 假设每包20ms
        SK_LOGI(TAG, "Estimated duration: %.2f seconds", estimatedDuration);

        // 计算数据利用率
        float dataUtilization = (float)totalBytes / downloadData->totalSize * 100;
        SK_LOGI(TAG, "Audio data ratio: %.1f%% (%zu/%zu bytes)",
               dataUtilization, totalBytes, downloadData->totalSize);

        SK_LOGI(TAG, "OGG parsing completed successfully!");
        SK_LOGI(TAG, "Ready for OPUS decoding: %dch @ %luHz",
               header.channels, header.sampleRate);
    } else {
        SK_LOGE(TAG, "No packets extracted");
    }
}

#ifndef TESTCASE_ENABLED
static void ReserveMemory(uint32_t **memBlock, uint32_t memBlockCnt, size_t blockSize) {
    for (int i = 0; i < memBlockCnt; i++) {
        // 不能超过配置门限，超过门限从PSRAM中分配，不能起到保留效果。所以分片分配.
        memBlock[i] = malloc(blockSize);
    }

    return;
}

static void FreeReserveMemory(uint32_t **memBlock, uint32_t memBlockCnt) {
    for (int i = 0; i < memBlockCnt; i++) {
        if (memBlock[i] != NULL) {
            free(memBlock[i]);
            memBlock[i] = NULL;
        }
    }

    return;
}
#endif

void app_main(void) {
#ifndef TESTCASE_ENABLED
    uint32_t *memResv[32];
#endif
    SK_LOGI(TAG, "Debug version at %s %s.", __DATE__, __TIME__);
    SkRledInit();
    SkRledSetEvent(SK_LED_EVENT_INIT);
    SK_OS_MODULE_MEM_STAT("start", false);
#ifndef TESTCASE_ENABLED
    ReserveMemory(memResv, ARRAY_SIZE(memResv), RESERVE_MEM_SIZE_PER_BLOCK);
    SK_OS_MODULE_MEM_STAT("Resv", true);
#endif
    ESP_ERROR_CHECK(SkBspBoardInit(16000, sizeof(uint16_t) * 8));
    SK_OS_MODULE_MEM_STAT("Bsp", true);
#ifndef TESTCASE_ENABLED
    g_smHandler = SkSmInit();
    SkConfigInit();
    SK_OS_MODULE_MEM_STAT("Config", true);
    SkWifiInit();
    SkWifiRegEventCb(SkSmOnWifiEvent);
    SK_OS_MODULE_MEM_STAT("WiFiTask", true);
    SkOtaManagerInit();
    SkOtaRegStateCallback(SkSmOtaOnStateChange);
    SK_OS_MODULE_MEM_STAT("OTA", true);
    SkOpusInit(16000, 1, 60);
    SK_OS_MODULE_MEM_STAT("OpusCodec", true);
    SkSrRegister(g_skSpeechMap, sizeof(g_skSpeechMap) / sizeof(SkSpeechMapItem), SkMainCmdProc);
    SK_OS_MODULE_MEM_STAT("Clink", false);
    SkClinkInit(g_smHandler);
    SK_OS_MODULE_MEM_STAT("Clink", true);
    SkRlinkInit();
    SkRlinkSetSmHandler(g_smHandler);
    SK_OS_MODULE_MEM_STAT("Rlink", true);
    SkWsInit();
    SkWsStart();
    SkWsRegOnBinDataCallback(SkRlinkFeedWebSocketAudio, NULL);
    SkWsRegOnTxtDataCallback(SkRlinkFeedWebSocketText, NULL);
    SK_OS_MODULE_MEM_STAT("WebSocket", true);
    SK_OS_MODULE_MEM_STAT("Audio", false);
    SkAudioInit(sizeof(uint16_t), 960);
    SkClinkSetFunFlag(CLINK_RUN_FLAG_IDLE);
    SkPlayerSetCallback(SkOpusDecFeedPlayAudio);
    SkSrSetSendFunc(SkOpusEncEnqueue);
    SkOpusEncSetCallback(SkRlinkFeedReordAudio);
    SkRlinkSetCodedDataCallback(SkOpusDecPlayRemote, SkOpusDecGetHandler());
    SkRlinkSetCodedDataEndCallback(SkOpusDecRemoteDataEnd, SkOpusDecGetHandler());
    SkPeripheralInit(g_smHandler);
    SK_OS_MODULE_MEM_STAT("Peripheral", true);
    FreeReserveMemory(memResv, ARRAY_SIZE(memResv));
    SK_OS_MODULE_MEM_STAT("Resv-End", true);
    SkWsSetServerIp("************", 8768);
    SK_OS_MODULE_MEM_STAT("WifiSta", false);
    SkSmSendEvent(g_smHandler, SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_INIT_OK, 0, 0);
    vTaskDelay(pdMS_TO_TICKS(15000));
    SK_OS_MODULE_MEM_STAT("WifiSta", true);
    SK_LOGI(TAG, "Auto-starting WebSocket connection...");
    SkWsStartConnect();
    SK_LOGI(TAG, "WebSocket connection started after WiFi ready");
    SkOpusDecUnmuteRemote();

    // 测试HTTP下载功能
    TestHttpDownload();
#else
    SkTestMain();
#endif
    return;
}
