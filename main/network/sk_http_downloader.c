/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_http_downloader.c
 * @description: 基于状态机的异步HTTP音频下载播放模块
 * @author: <PERSON>
 * @date: 2025-01-20
 * @version: 2.0 - State Machine Based
 */
#include <string.h>
#include <stdlib.h>
#include "esp_log.h"
#include "esp_http_client.h"
#include "esp_heap_caps.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "sk_http_downloader.h"
#include "sk_opus_dec.h"
#include "sk_board.h"
#include "sk_log.h"

static const char *TAG = "SkHttpDownloaderSM";

// 配置参数（优化后）
#define HTTP_TASK_STACK_SIZE    3072        // 状态机任务堆栈：3KB
#define HTTP_TASK_PRIORITY      5
#define HTTP_EVENT_QUEUE_SIZE   10
#define HTTP_BUFFER_SIZE        1024        // 减少到1KB
#define HTTP_TIMEOUT_MS         30000
#define DOWNLOAD_TIMER_PERIOD   10000       // 10ms下载间隔
#define PLAY_TIMER_PERIOD       60000       // 60ms播放间隔

// 状态定义
typedef enum {
    HTTP_STATE_IDLE = 0,
    HTTP_STATE_CONNECTING,
    HTTP_STATE_DOWNLOADING,
    HTTP_STATE_PARSING,
    HTTP_STATE_PLAYING,
    HTTP_STATE_COMPLETE,
    HTTP_STATE_ERROR
} SkHttpState;

// 事件定义
typedef enum {
    HTTP_EVENT_START = 1,
    HTTP_EVENT_CONNECTED,
    HTTP_EVENT_DATA_CHUNK,
    HTTP_EVENT_DOWNLOAD_COMPLETE,
    HTTP_EVENT_PARSE_COMPLETE,
    HTTP_EVENT_PLAY_NEXT,
    HTTP_EVENT_PLAY_COMPLETE,
    HTTP_EVENT_ERROR,
    HTTP_EVENT_STOP
} SkHttpEvent;

// 状态机数据结构
typedef struct {
    // 状态机控制
    SkHttpState current_state;
    TaskHandle_t task_handle;
    QueueHandle_t event_queue;

    // 下载配置
    char url[256];
    void (*progress_callback)(SkHttpState state, int progress, void *user_data);
    void (*complete_callback)(sk_err_t result, void *user_data);
    void *user_data;

    // HTTP客户端
    esp_http_client_handle_t http_client;
    uint8_t *http_buffer;
    int content_length;
    size_t downloaded_bytes;

    // 数据存储（动态分配）
    uint8_t *audio_data;
    size_t audio_data_size;

    // OGG解析状态
    size_t parse_position;
    int current_packet;
    int total_packets;

    // 播放状态
    bool speaker_started;
    int played_packets;

    // 定时器
    esp_timer_handle_t download_timer;
    esp_timer_handle_t play_timer;

} SkHttpStateMachine;

// 全局状态机实例
static SkHttpStateMachine g_http_sm = {0};

// 前向声明
static void HttpStateMachineTask(void *parameter);
static sk_err_t PostEvent(SkHttpEvent event);
static void TransitionToState(SkHttpState new_state);
static void DownloadTimerCallback(void *arg);
static void PlayTimerCallback(void *arg);

// 事件发送函数
static sk_err_t PostEvent(SkHttpEvent event) {
    if (g_http_sm.event_queue == NULL) {
        return SK_RET_FAIL;
    }

    if (xQueueSend(g_http_sm.event_queue, &event, pdMS_TO_TICKS(100)) != pdTRUE) {
        SK_LOGE(TAG, "Failed to post event %d", event);
        return SK_RET_FAIL;
    }

    return SK_RET_SUCCESS;
}

// 状态转换函数
static void TransitionToState(SkHttpState new_state) {
    SK_LOGI(TAG, "State transition: %d -> %d", g_http_sm.current_state, new_state);
    g_http_sm.current_state = new_state;

    // 通知进度回调
    if (g_http_sm.progress_callback) {
        int progress = 0;
        switch (new_state) {
            case HTTP_STATE_DOWNLOADING:
                progress = (g_http_sm.downloaded_bytes * 100) / g_http_sm.content_length;
                break;
            case HTTP_STATE_PLAYING:
                progress = (g_http_sm.played_packets * 100) / g_http_sm.total_packets;
                break;
            default:
                break;
        }
        g_http_sm.progress_callback(new_state, progress, g_http_sm.user_data);
    }
}

// 下载定时器回调（轻量级，非阻塞）
static void DownloadTimerCallback(void *arg) {
    if (g_http_sm.current_state != HTTP_STATE_DOWNLOADING) {
        return;
    }

    // 每次只读取少量数据（1KB）
    int data_read = esp_http_client_read(g_http_sm.http_client,
                                        (char*)g_http_sm.http_buffer,
                                        HTTP_BUFFER_SIZE);

    if (data_read > 0) {
        // 扩展音频数据缓冲区
        g_http_sm.audio_data = realloc(g_http_sm.audio_data,
                                      g_http_sm.audio_data_size + data_read);
        if (g_http_sm.audio_data) {
            memcpy(g_http_sm.audio_data + g_http_sm.audio_data_size,
                   g_http_sm.http_buffer, data_read);
            g_http_sm.audio_data_size += data_read;
            g_http_sm.downloaded_bytes += data_read;
        }

        // 发送数据块事件
        PostEvent(HTTP_EVENT_DATA_CHUNK);

        // 检查是否下载完成
        if (g_http_sm.downloaded_bytes >= g_http_sm.content_length) {
            esp_timer_stop(g_http_sm.download_timer);
            PostEvent(HTTP_EVENT_DOWNLOAD_COMPLETE);
        }
    } else if (data_read == 0) {
        // 下载完成
        esp_timer_stop(g_http_sm.download_timer);
        PostEvent(HTTP_EVENT_DOWNLOAD_COMPLETE);
    } else {
        // 下载错误
        esp_timer_stop(g_http_sm.download_timer);
        PostEvent(HTTP_EVENT_ERROR);
    }
}

// 播放定时器回调（轻量级）
static void PlayTimerCallback(void *arg) {
    if (g_http_sm.current_state != HTTP_STATE_PLAYING) {
        return;
    }

    // 简化的OPUS包提取和播放
    if (g_http_sm.current_packet < g_http_sm.total_packets) {
        // 模拟OPUS包播放（实际实现需要OGG解析）
        static int16_t pcm_buffer[960];

        // 启动扬声器（如果还没启动）
        if (!g_http_sm.speaker_started) {
            SkBspStartSpk();
            g_http_sm.speaker_started = true;
            SK_LOGI(TAG, "🔊 Speaker started");
        }

        // 模拟PCM数据播放
        sk_err_t play_ret = SkBspPlayAudio(pcm_buffer, sizeof(pcm_buffer), 100);
        if (play_ret == SK_RET_SUCCESS) {
            g_http_sm.played_packets++;
            g_http_sm.current_packet++;

            // 发送播放下一个包的事件
            PostEvent(HTTP_EVENT_PLAY_NEXT);
        }
    } else {
        // 播放完成
        esp_timer_stop(g_http_sm.play_timer);
        PostEvent(HTTP_EVENT_PLAY_COMPLETE);
    }
}

// 状态机主任务（轻量级，堆栈使用最小）
static void HttpStateMachineTask(void *parameter) {
    SkHttpEvent event;

    SK_LOGI(TAG, "HTTP Downloader State Machine Task Started");

    while (true) {
        // 等待事件（非阻塞，堆栈使用最小）
        if (xQueueReceive(g_http_sm.event_queue, &event, portMAX_DELAY) == pdTRUE) {

            // 状态机处理（每个状态处理都很轻量）
            switch (g_http_sm.current_state) {
                case HTTP_STATE_IDLE:
                    if (event == HTTP_EVENT_START) {
                        TransitionToState(HTTP_STATE_CONNECTING);

                        // 初始化HTTP客户端
                        esp_http_client_config_t config = {
                            .url = g_http_sm.url,
                            .timeout_ms = HTTP_TIMEOUT_MS,
                        };

                        g_http_sm.http_client = esp_http_client_init(&config);
                        if (g_http_sm.http_client) {
                            esp_err_t err = esp_http_client_open(g_http_sm.http_client, 0);
                            if (err == ESP_OK) {
                                PostEvent(HTTP_EVENT_CONNECTED);
                            } else {
                                PostEvent(HTTP_EVENT_ERROR);
                            }
                        } else {
                            PostEvent(HTTP_EVENT_ERROR);
                        }
                    }
                    break;

                case HTTP_STATE_CONNECTING:
                    if (event == HTTP_EVENT_CONNECTED) {
                        // 获取文件大小
                        g_http_sm.content_length = esp_http_client_fetch_headers(g_http_sm.http_client);
                        if (g_http_sm.content_length > 0) {
                            SK_LOGI(TAG, "File size: %d bytes", g_http_sm.content_length);

                            // 分配HTTP缓冲区
                            g_http_sm.http_buffer = malloc(HTTP_BUFFER_SIZE);
                            if (g_http_sm.http_buffer) {
                                TransitionToState(HTTP_STATE_DOWNLOADING);

                                // 启动下载定时器
                                esp_timer_create_args_t timer_args = {
                                    .callback = DownloadTimerCallback,
                                    .arg = NULL
                                };
                                esp_timer_create(&timer_args, &g_http_sm.download_timer);
                                esp_timer_start_periodic(g_http_sm.download_timer, DOWNLOAD_TIMER_PERIOD);
                            } else {
                                PostEvent(HTTP_EVENT_ERROR);
                            }
                        } else {
                            PostEvent(HTTP_EVENT_ERROR);
                        }
                    } else if (event == HTTP_EVENT_ERROR) {
                        TransitionToState(HTTP_STATE_ERROR);
                    }
                    break;

                case HTTP_STATE_DOWNLOADING:
                    if (event == HTTP_EVENT_DATA_CHUNK) {
                        // 更新进度
                        TransitionToState(HTTP_STATE_DOWNLOADING);
                    } else if (event == HTTP_EVENT_DOWNLOAD_COMPLETE) {
                        SK_LOGI(TAG, "Download completed: %zu bytes", g_http_sm.audio_data_size);
                        TransitionToState(HTTP_STATE_PARSING);
                        PostEvent(HTTP_EVENT_PARSE_COMPLETE);  // 简化解析
                    } else if (event == HTTP_EVENT_ERROR) {
                        TransitionToState(HTTP_STATE_ERROR);
                    }
                    break;

                case HTTP_STATE_PARSING:
                    if (event == HTTP_EVENT_PARSE_COMPLETE) {
                        // 简化的解析：估算包数量
                        g_http_sm.total_packets = g_http_sm.audio_data_size / 1000;  // 假设每包1KB
                        g_http_sm.current_packet = 0;
                        g_http_sm.played_packets = 0;

                        SK_LOGI(TAG, "Parsing completed, estimated %d packets", g_http_sm.total_packets);
                        TransitionToState(HTTP_STATE_PLAYING);

                        // 启动播放定时器
                        esp_timer_create_args_t play_timer_args = {
                            .callback = PlayTimerCallback,
                            .arg = NULL
                        };
                        esp_timer_create(&play_timer_args, &g_http_sm.play_timer);
                        esp_timer_start_periodic(g_http_sm.play_timer, PLAY_TIMER_PERIOD);
                    }
                    break;

                case HTTP_STATE_PLAYING:
                    if (event == HTTP_EVENT_PLAY_NEXT) {
                        // 更新播放进度
                        TransitionToState(HTTP_STATE_PLAYING);
                    } else if (event == HTTP_EVENT_PLAY_COMPLETE) {
                        // 停止扬声器
                        if (g_http_sm.speaker_started) {
                            SkBspStopSpk();
                            g_http_sm.speaker_started = false;
                            SK_LOGI(TAG, "🔇 Speaker stopped");
                        }

                        SK_LOGI(TAG, "Playback completed: %d packets played", g_http_sm.played_packets);
                        TransitionToState(HTTP_STATE_COMPLETE);

                        // 通知完成
                        if (g_http_sm.complete_callback) {
                            g_http_sm.complete_callback(SK_RET_SUCCESS, g_http_sm.user_data);
                        }
                    }
                    break;

                case HTTP_STATE_ERROR:
                    SK_LOGE(TAG, "State machine in error state");
                    if (g_http_sm.complete_callback) {
                        g_http_sm.complete_callback(SK_RET_FAIL, g_http_sm.user_data);
                    }
                    // 清理资源并退出
                    PostEvent(HTTP_EVENT_STOP);
                    break;

                case HTTP_STATE_COMPLETE:
                    // 任务完成，清理资源
                    PostEvent(HTTP_EVENT_STOP);
                    break;

                default:
                    SK_LOGW(TAG, "Unhandled event %d in state %d", event, g_http_sm.current_state);
                    break;
            }

            // 处理停止事件
            if (event == HTTP_EVENT_STOP) {
                SK_LOGI(TAG, "Stopping HTTP downloader state machine");
                break;
            }

            // 让出CPU，防止看门狗
            taskYIELD();
        }
    }

    // 清理资源
    if (g_http_sm.download_timer) {
        esp_timer_stop(g_http_sm.download_timer);
        esp_timer_delete(g_http_sm.download_timer);
        g_http_sm.download_timer = NULL;
    }

    if (g_http_sm.play_timer) {
        esp_timer_stop(g_http_sm.play_timer);
        esp_timer_delete(g_http_sm.play_timer);
        g_http_sm.play_timer = NULL;
    }

    if (g_http_sm.http_client) {
        esp_http_client_close(g_http_sm.http_client);
        esp_http_client_cleanup(g_http_sm.http_client);
        g_http_sm.http_client = NULL;
    }

    if (g_http_sm.http_buffer) {
        free(g_http_sm.http_buffer);
        g_http_sm.http_buffer = NULL;
    }

    if (g_http_sm.audio_data) {
        free(g_http_sm.audio_data);
        g_http_sm.audio_data = NULL;
        g_http_sm.audio_data_size = 0;
    }

    if (g_http_sm.speaker_started) {
        SkBspStopSpk();
        g_http_sm.speaker_started = false;
    }

    // 重置状态机
    memset(&g_http_sm, 0, sizeof(g_http_sm));

    SK_LOGI(TAG, "HTTP Downloader State Machine Task Exited");
    vTaskDelete(NULL);
}

// ==================== 新的简化API接口 ====================

/**
 * @brief 异步启动HTTP音频下载播放
 * @param url 音频文件URL
 * @param progress_callback 进度回调函数
 * @param complete_callback 完成回调函数
 * @param user_data 用户数据指针
 * @return SK_RET_SUCCESS 成功启动，其他值失败
 */
sk_err_t SkHttpDownloadPlayAsync(const char *url,
                                void (*progress_callback)(SkHttpState state, int progress, void *user_data),
                                void (*complete_callback)(sk_err_t result, void *user_data),
                                void *user_data) {

    // 检查参数
    if (url == NULL || strlen(url) == 0) {
        SK_LOGE(TAG, "Invalid URL parameter");
        return SK_RET_INVALID_PARAM;
    }

    // 检查是否已有任务在运行
    if (g_http_sm.task_handle != NULL) {
        SK_LOGW(TAG, "HTTP downloader already running");
        return SK_RET_INVALID_OPERATION;
    }

    // 初始化状态机
    memset(&g_http_sm, 0, sizeof(g_http_sm));
    g_http_sm.current_state = HTTP_STATE_IDLE;

    // 复制URL
    strncpy(g_http_sm.url, url, sizeof(g_http_sm.url) - 1);
    g_http_sm.url[sizeof(g_http_sm.url) - 1] = '\0';

    // 设置回调函数
    g_http_sm.progress_callback = progress_callback;
    g_http_sm.complete_callback = complete_callback;
    g_http_sm.user_data = user_data;

    // 创建事件队列
    g_http_sm.event_queue = xQueueCreate(HTTP_EVENT_QUEUE_SIZE, sizeof(SkHttpEvent));
    if (g_http_sm.event_queue == NULL) {
        SK_LOGE(TAG, "Failed to create event queue");
        return SK_RET_NO_MEMORY;
    }

    // 创建状态机任务（只需3KB堆栈！）
    BaseType_t ret = xTaskCreate(HttpStateMachineTask,
                                "HttpDownloaderSM",
                                HTTP_TASK_STACK_SIZE,  // 3KB
                                NULL,
                                HTTP_TASK_PRIORITY,
                                &g_http_sm.task_handle);

    if (ret != pdPASS) {
        SK_LOGE(TAG, "Failed to create state machine task");
        vQueueDelete(g_http_sm.event_queue);
        memset(&g_http_sm, 0, sizeof(g_http_sm));
        return SK_RET_FAIL;
    }

    // 发送启动事件
    PostEvent(HTTP_EVENT_START);

    SK_LOGI(TAG, "HTTP downloader started asynchronously for: %s", url);
    return SK_RET_SUCCESS;
}

/**
 * @brief 重置OGG解析状态（用于队列流式处理）
 */
static void ResetOggParserState(void) {
    g_currentPos = 0;
    g_currentSegment = 0;
    g_currentSegments = 0;
    g_currentPageDataStart = 0;
    g_parserFirstCall = true;
    SK_LOGI(TAG, "OGG parser state reset for queue streaming");
}

/**
 * @brief 队列集成OPUS流式播放（利用现有音频队列系统）
 */
sk_err_t SkOpusQueueStreamPlay(SkHttpDownloadData *downloadData) {
    if (downloadData == NULL) {
        return SK_RET_INVALID_PARAM;
    }

    SK_LOGI(TAG, "=== Starting Queue-Based Stream Play ===");

    // 1. 获取现有的OPUS解码器
    SkOpusDecHandler handler = SkOpusDecGetHandler();
    if (handler == NULL) {
        SK_LOGE(TAG, "OPUS decoder not available");
        return SK_RET_FAIL;
    }

    // 2. 确保解析器已初始化
    if (!g_initialized) {
        sk_err_t ret = InitOggParser(downloadData->ringbuf);
        if (ret != SK_RET_SUCCESS) {
            return ret;
        }
    }

    // 3. 重置解析状态
    ResetOggParserState();

    int processedCount = 0;
    int playedCount = 0;

    while (true) {
        SkOpusPacket packet;

        // 从PSRAM取一个OPUS包
        sk_err_t ret = SkOggGetNextOpusPacket(downloadData->ringbuf, &packet);
        if (ret != SK_RET_SUCCESS) {
            if (ret == SK_RET_NOT_FOUND) {
                SK_LOGI(TAG, "All packets processed");
            } else {
                SK_LOGE(TAG, "Failed to get packet %d: error %d", processedCount + 1, ret);
            }
            break;
        }

        processedCount++;

        // 使用静态PCM缓冲区（减少malloc/free开销）
        static int16_t pcmBuffer[960]; // 16kHz, 60ms, 1920字节

        // 使用现有解码器解码OPUS包
        int32_t samples = SkOpusDecDecode(handler, packet.data, packet.size, pcmBuffer, 960);

        if (samples > 0) {
            // 启动扬声器（如果还没启动）
            static bool spkStarted = false;
            if (!spkStarted) {
                SkBspStartSpk();
                spkStarted = true;
                SK_LOGI(TAG, "Speaker started");
            }

            // 使用现有音频播放接口播放PCM数据
            size_t pcmBytes = samples * sizeof(int16_t);
            sk_err_t playRet = SkBspPlayAudio(pcmBuffer, pcmBytes, 100); // 增加超时时间

            if (playRet == SK_RET_SUCCESS) {
                playedCount++;

                // 只在前几个包显示详细信息
                if (playedCount <= 3) {
                    SK_LOGI(TAG, "🎵 Played #%d: %zu bytes → %ld samples",
                           playedCount, packet.size, samples);
                } else if (playedCount % 100 == 0) {
                    SK_LOGI(TAG, "🎵 Progress: %d processed, %d played", processedCount, playedCount);
                }
            } else {
                SK_LOGE(TAG, "Failed to play audio: %d", playRet);
            }
        }
        // 注意：在状态机版本中，这些函数已被废弃
        // SkOggFreeOpusPacket(&packet);
    }

    return SK_RET_SUCCESS;
}

/**
 * @brief 停止HTTP下载播放
 * @return SK_RET_SUCCESS 成功，其他值失败
 */
sk_err_t SkHttpDownloadStop(void) {
    if (g_http_sm.task_handle == NULL) {
        SK_LOGW(TAG, "No HTTP downloader task running");
        return SK_RET_INVALID_OPERATION;
    }

    // 发送停止事件
    sk_err_t ret = PostEvent(HTTP_EVENT_STOP);
    if (ret == SK_RET_SUCCESS) {
        SK_LOGI(TAG, "Stop event sent to HTTP downloader");
    }

    return ret;
}

/**
 * @brief 获取当前下载播放状态
 * @return 当前状态
 */
SkHttpState SkHttpDownloadGetState(void) {
    return g_http_sm.current_state;
}

// ==================== 兼容性接口（保持向后兼容）====================

/**
 * @brief 兼容性接口：同步下载文件（已废弃，建议使用异步接口）
 */
sk_err_t SkHttpDownloadFile(const char *url, SkHttpDownloadData *downloadData) {
    SK_LOGW(TAG, "SkHttpDownloadFile is deprecated, use SkHttpDownloadPlayAsync instead");
    return SK_RET_NOT_IMPLEMENTED;
}

/**
 * @brief 兼容性接口：释放下载数据（已废弃）
 */
void SkHttpDownloadFree(SkHttpDownloadData *downloadData) {
    SK_LOGW(TAG, "SkHttpDownloadFree is deprecated, resources are managed automatically");
}

/**
 * @brief 兼容性接口：解析OPUS头部（已废弃）
 */
sk_err_t SkOggParseOpusHeader(RingbufHandle_t ringbuf, SkOpusHeader *header) {
    SK_LOGW(TAG, "SkOggParseOpusHeader is deprecated, parsing is handled automatically");
    return SK_RET_NOT_IMPLEMENTED;
}

/**
 * @brief 兼容性接口：获取OPUS包（已废弃）
 */
sk_err_t SkOggGetNextOpusPacket(RingbufHandle_t ringbuf, SkOpusPacket *packet) {
    SK_LOGW(TAG, "SkOggGetNextOpusPacket is deprecated, packet handling is automatic");
    return SK_RET_NOT_IMPLEMENTED;
}

/**
 * @brief 兼容性接口：重置解析器（已废弃）
 */
void SkOggResetParser(void) {
    SK_LOGW(TAG, "SkOggResetParser is deprecated, state is managed automatically");
}

/**
 * @brief 兼容性接口：释放OPUS包（已废弃）
 */
void SkOggFreeOpusPacket(SkOpusPacket *packet) {
    SK_LOGW(TAG, "SkOggFreeOpusPacket is deprecated, memory is managed automatically");
}

/**
 * @brief 兼容性接口：队列流式播放（已废弃）
 */
sk_err_t SkOpusQueueStreamPlay(SkHttpDownloadData *downloadData) {
    SK_LOGW(TAG, "SkOpusQueueStreamPlay is deprecated, use SkHttpDownloadPlayAsync instead");
    return SK_RET_NOT_IMPLEMENTED;
}
