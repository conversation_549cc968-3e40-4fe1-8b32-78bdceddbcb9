/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_http_downloader.h
 * @description: 基于状态机的异步HTTP音频下载播放模块头文件
 * @author: <PERSON>
 * @date: 2025-01-20
 * @version: 2.0 - State Machine Based
 */
#ifndef SK_HTTP_DOWNLOADER_H
#define SK_HTTP_DOWNLOADER_H

#include <stdint.h>
#include <stdbool.h>
#include "sk_common.h"
#include "freertos/FreeRTOS.h"
#include "freertos/ringbuf.h"

#ifdef __cplusplus
extern "C" {
#endif

// 状态机状态定义
typedef enum {
    HTTP_STATE_IDLE = 0,        ///< 空闲状态
    HTTP_STATE_CONNECTING,      ///< 连接中
    HTTP_STATE_DOWNLOADING,     ///< 下载中
    HTTP_STATE_PARSING,         ///< 解析中
    HTTP_STATE_PLAYING,         ///< 播放中
    HTTP_STATE_COMPLETE,        ///< 完成
    HTTP_STATE_ERROR           ///< 错误状态
} SkHttpState;

// ==================== 新的状态机API ====================

/**
 * @brief 异步启动HTTP音频下载播放
 * @param url 音频文件URL
 * @param progress_callback 进度回调函数，参数：(状态, 进度百分比, 用户数据)
 * @param complete_callback 完成回调函数，参数：(结果, 用户数据)
 * @param user_data 用户数据指针
 * @return SK_RET_SUCCESS 成功启动，其他值失败
 */
sk_err_t SkHttpDownloadPlayAsync(const char *url,
                                void (*progress_callback)(SkHttpState state, int progress, void *user_data),
                                void (*complete_callback)(sk_err_t result, void *user_data),
                                void *user_data);

/**
 * @brief 停止HTTP下载播放
 * @return SK_RET_SUCCESS 成功，其他值失败
 */
sk_err_t SkHttpDownloadStop(void);

/**
 * @brief 获取当前下载播放状态
 * @return 当前状态
 */
SkHttpState SkHttpDownloadGetState(void);

// ==================== 兼容性数据结构（已废弃）====================

/**
 * @brief HTTP下载数据结构（已废弃，仅为兼容性保留）
 */
typedef struct {
    RingbufHandle_t ringbuf;    ///< 环形缓冲区句柄
    uint8_t *storage;           ///< PSRAM存储指针
    size_t totalSize;           ///< 文件总大小
} SkHttpDownloadData;

/**
 * @brief OPUS头部信息结构（已废弃，仅为兼容性保留）
 */
typedef struct {
    uint8_t version;        ///< OPUS版本
    uint8_t channels;       ///< 声道数
    uint16_t preSkip;       ///< 预跳过样本数
    uint32_t sampleRate;    ///< 采样率
    uint16_t outputGain;    ///< 输出增益
} SkOpusHeader;

/**
 * @brief OPUS音频包结构（已废弃，仅为兼容性保留）
 */
typedef struct {
    uint8_t *data;          ///< OPUS包数据
    size_t size;            ///< 包大小
} SkOpusPacket;

// ==================== 兼容性API（已废弃）====================

/**
 * @brief 下载文件到PSRAM环形缓冲区（已废弃）
 * @param url 文件URL
 * @param downloadData 输出下载数据结构
 * @return SK_RET_SUCCESS 成功，其他值失败
 */
sk_err_t SkHttpDownloadFile(const char *url, SkHttpDownloadData *downloadData);

/**
 * @brief 释放下载资源
 * @param downloadData 下载数据结构
 */
void SkHttpDownloadFree(SkHttpDownloadData *downloadData);

/**
 * @brief 从环形缓冲区解析OPUS头部信息
 * @param ringbuf 环形缓冲区句柄
 * @param header 输出OPUS头部信息
 * @return SK_RET_SUCCESS 成功，其他值失败
 */
sk_err_t SkOggParseOpusHeader(RingbufHandle_t ringbuf, SkOpusHeader *header);

/**
 * @brief 从环形缓冲区提取下一个OPUS音频包
 * @param ringbuf 环形缓冲区句柄
 * @param packet 输出OPUS包
 * @return SK_RET_SUCCESS 成功，SK_RET_NOT_FOUND 没有更多包，其他值失败
 */
sk_err_t SkOggGetNextOpusPacket(RingbufHandle_t ringbuf, SkOpusPacket *packet);

/**
 * @brief 重置OGG解析状态（用于重新开始解析）
 */
void SkOggResetParser(void);

/**
 * @brief 释放OPUS包资源
 * @param packet OPUS包
 */
void SkOggFreeOpusPacket(SkOpusPacket *packet);

/**
 * @brief 使用现有解码器解码OPUS包
 * @param packet OPUS数据包
 * @param pcmBuffer PCM输出缓冲区
 * @param pcmSize PCM缓冲区大小（样本数）
 * @return 解码后的PCM样本数，失败返回0
 */
int32_t SkOpusAdapterDecode(SkOpusPacket *packet, int16_t *pcmBuffer, int32_t pcmSize);

/**
 * @brief 重置OGG解析位置（已废弃）
 */
void SkOggResetParserPosition(void);

/**
 * @brief 队列集成OPUS流式播放（已废弃）
 * @param downloadData 下载的数据
 * @return SK_RET_SUCCESS 成功，其他值失败
 */
sk_err_t SkOpusQueueStreamPlay(SkHttpDownloadData *downloadData);

#ifdef __cplusplus
}
#endif

#endif // SK_HTTP_DOWNLOADER_H
